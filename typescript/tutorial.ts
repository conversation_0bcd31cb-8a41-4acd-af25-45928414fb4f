import { Eval } from "braintrust";
import { <PERSON><PERSON>htein } from "autoevals";
 
const logger = initLogger({
projectName: "pedro-project1",
apiKey: "sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU"
});

Eval(
  "pedro-project1", // Replace with your project name
  {
    data: () => {
      return [
        {
          input: "Foo",
          expected: "Hi Foo",
        },
        {
          input: "Bar",
          expected: "Hello Bar",
        },
      ]; // Replace with your eval dataset
    },
    task: async (input) => {
      return "Hi " + input; // Replace with your LLM call
    },
    scores: [Levenshtein],
  },
);