import { initLogger, wrapOpenA<PERSON> } from "braintrust";
import OpenAI from "openai";

// import { globalOpenAIConfig } from "./util";

export enum SupportedOpenAIModel {
GPT_4_1 = "gpt-4.1",
GPT_4_1_MINI = "gpt-4.1-mini",
GPT_4_1_NANO = "gpt-4.1-nano",
GPT_4O = "gpt-4o",
O3 = "o3",
O4_MINI = "o4-mini"
}

export type OpenAIRequest = {
model: SupportedOpenAIModel;
parameters: Omit<OpenAI.Responses.ResponseCreateParams, "model">;
options?: OpenAI.RequestOptions;
};

export async function callOpenAI(request: OpenAIRequest): Promise<OpenAI.Responses.Response> {
const { parameters, options } = request;
// const client = wrapOpenAI(new OpenAI(globalOpenAIConfig()));
const client = new OpenAI({
apiKey: "********************************************************************************************************************************************************************"})

const logger = initLogger({
projectName: "pedro-project1",
apiKey: "sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU"
});
// const client = new OpenAI(globalOpenAIConfig());

const response = await client.responses.create({
model: request.model,
...parameters,
...options
});

return response as OpenAI.Responses.Response;
}

export function getTemperature(model: SupportedOpenAIModel, temperature?: number): number {
if (model.toLocaleLowerCase().startsWith("o")) {
return 1;
}

return temperature ?? 0.5;
}